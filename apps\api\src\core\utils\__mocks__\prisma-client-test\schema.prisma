generator client {
  provider = "prisma-client-js"
  output   = "../src/core/utils/__mocks__/prisma-client-test"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id              String           @id @default(uuid())
  username        String           @unique
  email           String           @unique
  password        String
  name            String
  role            UserRole         @default(USER)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  isActive        Boolean          @default(true)
  customForms     CustomForm[]
  declarations    Declaration[]
  reportTemplates ReportTemplate[]
  sessions        Session[]

  @@map("users")
}

model Client {
  id           String        @id @default(uuid())
  clientNumber String?
  taxNumber    String        @unique
  clientName   String
  companyName  String?
  addedDate    DateTime      @default(now())
  phone        String?
  email        String?
  address      String?
  pdfFile      String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  declarations Declaration[]

  @@map("clients")
}

model Declaration {
  id                      String                   @id @default(uuid())
  declarationNumber       String                   @unique
  taxNumber               String
  clientName              String
  companyName             String?
  policyNumber            String?
  invoiceNumber           String?
  gatewayEntryNumber      String
  declarationType         DeclarationType
  declarationDate         DateTime
  count                   Int?
  weight                  Float?
  goodsType               GoodsType?
  itemsCount              Int?
  entryDate               DateTime?
  exitDate                DateTime?
  pdfFile                 String?
  clientId                String?
  userId                  String?
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  authorizations          Authorization[]
  client                  Client?                  @relation(fields: [clientId], references: [id])
  createdBy               User?                    @relation(fields: [userId], references: [id])
  drivers                 Driver[]
  itemMovements           ItemMovement[]
  nonReturnableGuarantees NonReturnableGuarantee[]
  permits                 Permit[]
  receipts                Receipt[]
  releases                Release[]
  returnableGuarantees    ReturnableGuarantee[]

  @@map("declarations")
}

model Driver {
  id            String      @id @default(uuid())
  declarationId String
  driverName    String
  truckNumber   String
  trailerNumber String?
  driverPhone   String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  declaration   Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("drivers")
}

model ItemMovement {
  id            String      @id @default(uuid())
  declarationId String
  itemName      String
  quantity      Int
  unit          String
  movementDate  DateTime
  movementType  String
  notes         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  declaration   Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@index([declarationId])
  @@index([itemName])
  @@index([movementDate])
  @@index([movementType])
  @@index([createdAt])
  @@map("item_movements")
}

model Authorization {
  id                  String            @id @default(uuid())
  authorizationNumber String            @unique
  clientName          String
  taxNumber           String
  authorizationType   AuthorizationType
  startDate           DateTime
  endDate             DateTime
  pdfFile             String?
  declarationId       String?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  declaration         Declaration?      @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("authorizations")
}

model Release {
  id               String      @id @default(uuid())
  releaseNumber    String      @unique
  issuingAuthority String
  invoiceNumber    String
  invoiceDate      DateTime?
  invoiceValue     Float?
  approvalDate     DateTime
  releaseStartDate DateTime
  releaseEndDate   DateTime
  driverPermit     Boolean     @default(false)
  pdfFile          String?
  declarationId    String
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
  declaration      Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("releases")
}

model Permit {
  id                String       @id @default(uuid())
  permitNumber      String       @unique
  declarationNumber String?
  issuingAuthority  String
  permitDate        DateTime
  pdfFile           String?
  declarationId     String?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  declaration       Declaration? @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("permits")
}

model ReturnableGuarantee {
  id                  String        @id @default(uuid())
  guaranteeSlipNumber String        @unique
  declarationNumber   String?
  guaranteeType       GuaranteeType
  guaranteeStartDate  DateTime
  guaranteeEndDate    DateTime
  clientName          String
  invoiceValue        Float?
  invoiceNumber       String?
  originCertNumber    String?
  packingListNumber   String?
  countryOfOrigin     String?
  declarationValue    Float?
  guaranteeAmount     Float?
  guaranteeDate       DateTime?
  invoiceDate         DateTime?
  pdfFile             String?
  declarationId       String?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt
  declaration         Declaration?  @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("returnable_guarantees")
}

model NonReturnableGuarantee {
  id                 String       @id @default(uuid())
  bankSlipNumber     String       @unique
  declarationNumber  String?
  clientName         String
  confiscationDate   DateTime?
  confiscatedAmount  Float?
  confiscationReason String?
  notes              String?
  pdfFile            String?
  declarationId      String?
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  declaration        Declaration? @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("non_returnable_guarantees")
}

model Receipt {
  id                String       @id @default(uuid())
  receiptNumber     String       @unique
  declarationNumber String?
  receiptType       ReceiptType?
  invoiceDate       DateTime?
  invoiceValue      Float?
  pdfFile           String?
  declarationId     String?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  declaration       Declaration? @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("receipts")
}

model OfficeDocument {
  id             String        @id @default(uuid())
  documentNumber String        @unique
  documentType   DocumentType?
  documentDate   DateTime
  documentValue  Float?
  pdfFile        String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@map("office_documents")
}

model InvalidatedToken {
  id            String    @id @default(uuid())
  token         String    @unique
  tokenType     TokenType
  userId        String
  expiresAt     DateTime
  invalidatedAt DateTime  @default(now())

  @@map("invalidated_tokens")
}

model Session {
  id           String   @id @default(uuid())
  userId       String
  ipAddress    String?
  userAgent    String?
  deviceInfo   String?
  isActive     Boolean  @default(true)
  lastActivity DateTime @default(now())
  createdAt    DateTime @default(now())
  expiresAt    DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model LoginAttempt {
  id            String      @id @default(uuid())
  username      String
  ipAddress     String?
  userAgent     String?
  status        LoginStatus
  attemptTime   DateTime    @default(now())
  failureReason String?

  @@map("login_attempts")
}

model AuditLog {
  id        String   @id @default(uuid())
  tableName String
  operation String
  action    String?
  recordId  String
  oldValues String?
  newValues String?
  userId    String?
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())

  @@map("audit_logs")
}

model CustomForm {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  formData    String
  formType    String?
  isActive    Boolean  @default(true)
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id])

  @@map("custom_forms")
}

model ReportTemplate {
  id          String   @id @default(uuid())
  name        String
  description String?
  template    String
  reportType  String
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id])

  @@map("report_templates")
}

model SystemSettings {
  id              String   @id @default("default")
  companyName     String   @default("نظام النور للأرشفة")
  companyLogo     String?
  companyAddress  String?
  companyPhone    String?
  companyEmail    String?
  companyWebsite  String?
  primaryColor    String   @default("#1976d2")
  secondaryColor  String   @default("#dc004e")
  defaultFont     String   @default("Tajawal")
  defaultLanguage String   @default("ar")
  maxFileSize     Int      @default(*********)
  enablePrinting  Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("system_settings")
}

enum UserRole {
  ADMIN
  USER
  MANAGER
}

enum DeclarationType {
  IMPORT
  EXPORT
}

enum GoodsType {
  HUMAN_MEDICINE
  LABORATORY_SOLUTIONS
  MEDICAL_SUPPLIES
  SUGAR_STRIPS
  MEDICAL_DEVICES
  MISCELLANEOUS
}

enum PackageType {
  DRUM
  CARTON
  BARREL
}

enum GuaranteeType {
  DOCUMENTS
  FINANCIAL
}

enum ReceiptType {
  FOLLOW_UP
  CLEARANCE
  RECEIPT
  DELIVERY
}

enum DocumentType {
  INVOICE
  CERTIFICATE
  PERMIT
  AUTHORIZATION
  GUARANTEE
  RECEIPT
  RELEASE
  OTHER
}

enum AuthorizationType {
  FOLLOW_UP
  CLEARANCE
  RECEIPT
  FULL
}

enum Currency {
  USD
  EUR
  GBP
  SAR
}

enum GuaranteeStatus {
  ACTIVE
  RETURNED
  EXPIRED
}

enum TokenType {
  ACCESS
  REFRESH
}

enum LoginStatus {
  SUCCESS
  FAILED
  LOCKED
  SUSPICIOUS
}
